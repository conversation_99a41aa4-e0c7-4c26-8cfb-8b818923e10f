"""
ROV 统一物理计算模块
整合浮力、控制、阻尼和推进器控制功能
为 standalone 模式提供完整的物理计算接口

这个模块将原本分散在多个 ActionGraph 脚本中的功能
重构为可以直接在 Python 中调用的统一接口
"""

import sys
import os

# 添加 scripts 目录到路径，以便导入其他模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入重构后的模块
from buoyancy_control import calculate_buoyancy_force, calculate_submerged_info
from buoyancy_forces import calculate_rotated_buoyancy_force, calculate_buoyancy_torque
from controller import PIDController, calculate_depth_control_force, calculate_attitude_control_force
from damping import calculate_damping_coefficients, calculate_drag_force, calculate_environmental_damping
from linear_angular_control import ThrusterController, calculate_thruster_allocation, calculate_6dof_thruster_forces


class ROVPhysicsEngine:
    """
    ROV 物理引擎类
    
    整合所有物理计算功能，提供统一的接口
    """
    
    def __init__(self, rov_config):
        """
        初始化 ROV 物理引擎
        
        参数:
            rov_config: ROV 配置字典
                {
                    'mass': float,           # 质量 (kg)
                    'volume': float,         # 体积 (m³)
                    'size': float,           # 尺寸 (m)
                    'target_depth': float,   # 目标深度 (m)
                    'max_thrust': float      # 最大推力 (N)
                }
        """
        self.config = rov_config
        
        # 计算体积（如果未提供）
        if 'volume' not in rov_config:
            size = rov_config['size']
            self.config['volume'] = size ** 3  # 立方体体积
        
        # 初始化控制器
        self.depth_controller = PIDController(
            kp=self._get_adaptive_gain(), 
            ki=10.0, 
            kd=0.01
        )
        self.attitude_controller = PIDController(kp=100.0, ki=10.0, kd=0.01)
        self.thruster_controller = ThrusterController(
            max_thrust=rov_config.get('max_thrust', 1000.0)
        )
        
        # 物理常数
        self.water_density = 1025.0  # 海水密度 kg/m³
        self.gravity = 9.81          # 重力加速度 m/s²
        self.max_damping = 50.0      # 最大阻尼系数
    
    def _get_adaptive_gain(self):
        """根据 ROV 质量计算自适应控制增益"""
        mass = self.config['mass']
        if mass < 600:
            return 300.0
        elif mass < 900:
            return 500.0
        else:
            return 700.0
    
    def calculate_rotated_buoyancy_physics(self, position, rotation_angles):
        """
        计算考虑旋转姿态的浮力

        参数:
            position: 当前位置 [x, y, z]
            rotation_angles: 旋转角度 [roll, pitch, yaw] (度)

        返回:
            dict: 旋转浮力计算结果
        """
        return calculate_rotated_buoyancy_force(
            volume=self.config['volume'],
            height=self.config['size'],
            z_position=position[2],
            rotation_angles=rotation_angles,
            water_density=self.water_density,
            gravity=self.gravity
        )

    def calculate_buoyancy_torque_physics(self, position, rotation_angles, center_of_buoyancy_offset=[0, 0, 0]):
        """
        计算浮力产生的力矩

        参数:
            position: 当前位置 [x, y, z]
            rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
            center_of_buoyancy_offset: 浮心相对于质心的偏移 [x, y, z] (m)

        返回:
            dict: 浮力力矩计算结果
        """
        return calculate_buoyancy_torque(
            volume=self.config['volume'],
            height=self.config['size'],
            z_position=position[2],
            rotation_angles=rotation_angles,
            center_of_buoyancy_offset=center_of_buoyancy_offset,
            water_density=self.water_density,
            gravity=self.gravity
        )

    def calculate_complete_physics(self, position, velocity, last_position, dt, joystick_input=None):
        """
        计算完整的 ROV 物理状态
        
        参数:
            position: 当前位置 [x, y, z] (m)
            velocity: 当前速度 [vx, vy, vz] (m/s)
            last_position: 上一帧位置 [x, y, z] (m)
            dt: 时间步长 (s)
            joystick_input: 操纵杆输入 {'x': float, 'y': float}，可选
            
        返回:
            dict: 完整的物理计算结果
        """
        z_pos = position[2]
        height = self.config['size']
        volume = self.config['volume']
        target_depth = self.config['target_depth']
        
        # 1. 浮力计算
        buoyancy_result = calculate_buoyancy_force(
            volume, height, z_pos, self.water_density, self.gravity
        )
        
        # 2. 深度控制力计算
        control_result = calculate_depth_control_force(
            z_pos, target_depth, self.depth_controller
        )
        
        # 3. 阻尼计算
        damping_result = calculate_environmental_damping(
            z_pos, height, velocity, self.max_damping
        )
        
        # 4. 推进器控制（如果有操纵杆输入）
        thruster_result = None
        if joystick_input:
            thruster_result = calculate_thruster_allocation(
                joystick_input.get('y', 0.0),
                joystick_input.get('x', 0.0),
                self.thruster_controller.max_thrust
            )
        
        # 5. 合力计算
        drag_force_z = damping_result['drag_force'][2] if isinstance(damping_result['drag_force'], list) else damping_result['drag_force']
        total_force_z = (
            buoyancy_result['buoyancy_force'] +
            control_result['control_force'] +
            drag_force_z
        )
        
        return {
            'buoyancy': buoyancy_result,
            'control': control_result,
            'damping': damping_result,
            'thrusters': thruster_result,
            'total_force': {
                'x': 0.0,
                'y': 0.0,
                'z': total_force_z
            },
            'physics_summary': {
                'depth': buoyancy_result['depth'],
                'buoyancy_force': buoyancy_result['buoyancy_force'],
                'control_force': control_result['control_force'],
                'drag_force': drag_force_z,
                'total_force_z': total_force_z
            }
        }

# ==================== 便捷函数 ====================

def create_rov_physics_engine(mass, size, target_depth, max_thrust=1000.0):
    """
    创建 ROV 物理引擎的便捷函数
    
    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        target_depth: 目标深度 (m)
        max_thrust: 最大推力 (N)
        
    返回:
        ROVPhysicsEngine: 配置好的物理引擎实例
    """
    rov_config = {
        'mass': mass,
        'size': size,
        'volume': size ** 3,
        'target_depth': target_depth,
        'max_thrust': max_thrust
    }
    return ROVPhysicsEngine(rov_config)

def calculate_simple_rov_physics(mass, size, target_depth, current_position, velocity, dt):
    """
    简化的 ROV 物理计算函数
    
    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        target_depth: 目标深度 (m)
        current_position: 当前位置 [x, y, z] (m)
        velocity: 当前速度 [vx, vy, vz] (m/s)
        dt: 时间步长 (s)
        
    返回:
        dict: 简化的物理计算结果
    """
    # 创建临时物理引擎
    engine = create_rov_physics_engine(mass, size, target_depth)
    
    # 计算物理状态
    last_position = [
        current_position[0] - velocity[0] * dt,
        current_position[1] - velocity[1] * dt,
        current_position[2] - velocity[2] * dt
    ]
    
    result = engine.calculate_complete_physics(
        current_position, velocity, last_position, dt
    )
    
    return result['physics_summary']

def calculate_rov_rotated_buoyancy(mass, size, position, rotation_angles,
                                 water_density=1025.0, gravity=9.81):
    """
    计算 ROV 旋转浮力的便捷函数

    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        position: 当前位置 [x, y, z]
        rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 旋转浮力计算结果
    """
    volume = size ** 3  # 立方体体积

    return calculate_rotated_buoyancy_force(
        volume=volume,
        height=size,
        z_position=position[2],
        rotation_angles=rotation_angles,
        water_density=water_density,
        gravity=gravity
    )

def calculate_rov_buoyancy_torque(mass, size, position, rotation_angles,
                                center_of_buoyancy_offset=[0, 0, 0],
                                water_density=1025.0, gravity=9.81):
    """
    计算 ROV 浮力力矩的便捷函数

    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        position: 当前位置 [x, y, z]
        rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
        center_of_buoyancy_offset: 浮心偏移 [x, y, z] (m)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 浮力力矩计算结果
    """
    volume = size ** 3  # 立方体体积

    return calculate_buoyancy_torque(
        volume=volume,
        height=size,
        z_position=position[2],
        rotation_angles=rotation_angles,
        center_of_buoyancy_offset=center_of_buoyancy_offset,
        water_density=water_density,
        gravity=gravity
    )

# ==================== 测试函数 ====================

def test_unified_physics():
    """测试统一物理计算模块"""
    print("🧪 测试 ROV 统一物理计算模块 (rov_physics_unified.py)")
    print("="*60)

    print("📖 模块功能说明:")
    print("   - 整合所有物理计算功能的统一接口")
    print("   - 支持基础浮力和旋转浮力计算")
    print("   - 集成 PID 控制、阻尼计算、推进器控制")
    print("   - 提供完整的 ROV 物理仿真解决方案")
    print("   - 支持多种便捷调用方式")

    # 测试配置
    test_configs = [
        {'mass': 800, 'size': 1.8, 'target_depth': -1.5, 'name': 'ROV_Original'},
        {'mass': 1000, 'size': 2.0, 'target_depth': -4.0, 'name': 'ROV_Main'},
        {'mass': 500, 'size': 1.6, 'target_depth': -2.0, 'name': 'ROV_Scout'}
    ]

    print(f"\n1️⃣ 测试统一物理引擎:")
    for config in test_configs:
        print(f"\n🤖 测试 {config['name']}:")

        # 创建物理引擎
        engine = create_rov_physics_engine(
            config['mass'], config['size'], config['target_depth']
        )

        # 测试位置
        test_position = [0, 0, -1.0]  # 1米深度
        test_velocity = [0, 0, -0.1]  # 下沉速度
        test_last_pos = [0, 0, -0.9]

        # 计算物理状态
        result = engine.calculate_complete_physics(
            test_position, test_velocity, test_last_pos, 1/60
        )

        summary = result['physics_summary']
        print(f"     深度: {summary['depth']:.2f}m")
        print(f"     浮力: {summary['buoyancy_force']:.1f}N")
        print(f"     控制力: {summary['control_force']:.1f}N")
        print(f"     总力: {summary['total_force_z']:.1f}N")

def test_rotated_buoyancy_integration():
    """测试旋转浮力集成"""
    print(f"\n2️⃣ 测试旋转浮力集成:")

    # 创建测试引擎
    engine = create_rov_physics_engine(mass=1000, size=2.0, target_depth=-3.0)

    # 测试不同旋转角度
    test_rotations = [
        {'angles': [0, 0, 0], 'desc': '无旋转'},
        {'angles': [30, 0, 0], 'desc': '横滚30度'},
        {'angles': [0, 45, 0], 'desc': '俯仰45度'},
        {'angles': [15, 30, 45], 'desc': '复合旋转'}
    ]

    position = [0, 0, -2.0]  # 2米深度

    for test in test_rotations:
        result = engine.calculate_rotated_buoyancy_physics(position, test['angles'])
        force = result['force_vector']

        print(f"   {test['desc']} {test['angles']}:")
        print(f"     浮力向量: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
        print(f"     原始浮力: {result['original_force']:.1f} N")

def test_convenience_functions():
    """测试便捷函数"""
    print(f"\n3️⃣ 测试便捷函数:")

    # 测试简化物理计算
    print("   简化物理计算:")
    result = calculate_simple_rov_physics(
        mass=1000, size=2.0, target_depth=-3.0,
        current_position=[0, 0, -1.5],
        velocity=[0, 0, -0.2],
        dt=1/60
    )

    print(f"     深度: {result['depth']:.2f}m")
    print(f"     浮力: {result['buoyancy_force']:.1f}N")
    print(f"     控制力: {result['control_force']:.1f}N")
    print(f"     总力: {result['total_force_z']:.1f}N")

    # 测试旋转浮力便捷函数
    print("\n   旋转浮力便捷函数:")
    buoyancy_result = calculate_rov_rotated_buoyancy(
        mass=1000, size=2.0,
        position=[0, 0, -2.0],
        rotation_angles=[20, 30, 40]
    )

    force = buoyancy_result['force_vector']
    print(f"     旋转浮力: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
    print(f"     浸没比例: {buoyancy_result['submerged_ratio']:.2f}")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 创建统一物理引擎")
    print("```python")
    print("from scripts.rov_physics_unified import create_rov_physics_engine")
    print("")
    print("# 创建 ROV 物理引擎")
    print("engine = create_rov_physics_engine(")
    print("    mass=1000.0,        # 1000kg ROV")
    print("    size=2.0,           # 2m x 2m x 2m")
    print("    target_depth=-3.0   # 目标深度 3m")
    print(")")
    print("")
    print("# 计算完整物理状态")
    print("result = engine.calculate_complete_physics(")
    print("    position=[0, 0, -1.5],")
    print("    velocity=[0, 0, -0.1],")
    print("    last_position=[0, 0, -1.4],")
    print("    dt=1/60")
    print(")")
    print("```")

    print("\n💡 示例 2: 使用便捷函数")
    print("```python")
    print("from scripts.rov_physics_unified import calculate_simple_rov_physics")
    print("")
    print("# 一次性计算所有物理量")
    print("summary = calculate_simple_rov_physics(")
    print("    mass=800, size=1.8, target_depth=-2.0,")
    print("    current_position=[0, 0, -1.0],")
    print("    velocity=[0, 0, -0.15],")
    print("    dt=1/60")
    print(")")
    print("print(f'深度: {summary[\"depth\"]:.2f}m')")
    print("```")

    print("\n💡 示例 3: 旋转浮力计算")
    print("```python")
    print("from scripts.rov_physics_unified import calculate_rov_rotated_buoyancy")
    print("")
    print("# 计算考虑旋转的浮力")
    print("buoyancy = calculate_rov_rotated_buoyancy(")
    print("    mass=1000, size=2.0,")
    print("    position=[0, 0, -2.0],")
    print("    rotation_angles=[15, 30, 45]  # Roll, Pitch, Yaw")
    print(")")
    print("```")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_unified_physics()
    test_rotated_buoyancy_integration()
    test_convenience_functions()
    demo_usage_examples()
    print("\n✅ ROV 统一物理引擎测试完成！")
    print("\n🌟 这是所有物理计算模块的集成中心")
    print("🔗 包含的子模块:")
    print("   - buoyancy_control.py: 基础浮力")
    print("   - buoyancy_forces.py: 旋转浮力")
    print("   - controller.py: PID 控制")
    print("   - damping.py: 阻尼计算")
    print("   - linear_angular_control.py: 推进器控制")
