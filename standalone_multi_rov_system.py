# Copyright (c) 2021-2024, NVIDIA CORPORATION. All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto. Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.
#

"""
Standalone Multi-ROV System - 独立模式版本
从 ActionGraph 模式转换为独立 Python 脚本
基于官方 Isaac Sim standalone 模式最佳实践
"""

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

import carb
import omni
import math
import time
import os
import sys
from isaacsim.core.api import SimulationContext
from pxr import UsdGeom, Gf, UsdPhysics, Usd
from omni.isaac.dynamic_control import _dynamic_control

# 添加 scripts 目录到路径，以便导入物理计算模块
current_dir = os.path.dirname(os.path.abspath(__file__))
scripts_dir = os.path.join(current_dir, "scripts")
if scripts_dir not in sys.path:
    sys.path.append(scripts_dir)

# 导入重构后的物理计算模块
try:
    from rov_physics_unified import ROVPhysicsEngine, create_rov_physics_engine, calculate_simple_rov_physics
    print("✅ 成功导入统一物理计算模块")
    USE_UNIFIED_PHYSICS = True
except ImportError as e:
    print(f"⚠️ 无法导入统一物理模块: {e}")
    print("🔄 将使用内置的简化物理计算")
    USE_UNIFIED_PHYSICS = False


# 初始化 Isaac Sim 核心组件
stage = simulation_app.context.get_stage()
sim_context = SimulationContext(stage_units_in_meters=1.0)

# 获取 PhysX 接口
physx_interface = omni.physx.acquire_physx_interface()

# ⚠️ 重要：加载 USD 环境文件 (类似于手动导入操作)
def load_usd_environment():
    """加载 USD 环境文件 - 替代手动导入操作"""
    print("🌊 加载 USD 环境文件...")

    usd_file_path = "ROV_THRUSTERS.usd"  # 请根据实际路径调整

    try:
        if not os.path.exists(usd_file_path):
            print(f"⚠️ 未找到 USD 文件: {usd_file_path}")
            print("🔧 将创建基础水下环境...")
            create_basic_underwater_environment()
            return True

        print(f"📁 找到 USD 文件: {usd_file_path}")

        # 获取绝对路径
        abs_path = os.path.abspath(usd_file_path)
        print(f"📍 绝对路径: {abs_path}")

        # 使用 omni.usd 直接打开（最接近双击行为）
        print("🔄 直接打开USD文件作为主场景...")

        # 关闭当前场景
        omni.usd.get_context().close_stage()

        # 直接打开USD文件
        success = omni.usd.get_context().open_stage(abs_path)

        if success:
            print("✅ USD文件已作为主场景打开")

            # 重新获取stage（因为我们打开了新的场景）
            global stage
            stage = omni.usd.get_context().get_stage()

            # 打印场景结构信息
            print("📋 场景结构:")
            root_prim = stage.GetPseudoRoot()
            for child in root_prim.GetChildren():
                print(f"  � /{child.GetName()}")

            return True
        else:
            print("❌ 打开USD文件失败，使用基础环境")
            create_basic_underwater_environment()
            return True

    except Exception as e:
        print(f"❌ 加载 USD 环境失败: {e}")
        print("🔧 将创建基础水下环境...")
        create_basic_underwater_environment()
        return True

def create_basic_underwater_environment():
    """创建基础水下环境（如果没有 USD 文件）"""
    print("🌊 创建基础水下环境...")

    # 创建水面平面
    water_surface = UsdGeom.Mesh.Define(stage, "/World/WaterSurface")
    water_surface.CreatePointsAttr([
        (-50, -50, 0), (50, -50, 0), (50, 50, 0), (-50, 50, 0)
    ])
    water_surface.CreateFaceVertexIndicesAttr([0, 1, 2, 3])
    water_surface.CreateFaceVertexCountsAttr([4])
    water_surface.CreateDisplayColorAttr([(0.2, 0.6, 0.8)])  # 蓝色水面

    # 创建海底
    sea_floor = UsdGeom.Mesh.Define(stage, "/World/SeaFloor")
    sea_floor.CreatePointsAttr([
        (-100, -100, -10), (100, -100, -10), (100, 100, -10), (-100, 100, -10)
    ])
    sea_floor.CreateFaceVertexIndicesAttr([0, 1, 2, 3])
    sea_floor.CreateFaceVertexCountsAttr([4])
    sea_floor.CreateDisplayColorAttr([(0.4, 0.3, 0.2)])  # 棕色海底

    # 添加一些环境光
    light = UsdGeom.DistantLight.Define(stage, "/World/SunLight")
    light.CreateIntensityAttr(1000)
    light.CreateColorAttr((0.8, 0.9, 1.0))  # 略带蓝色的光

    print("✅ 基础水下环境创建完成")

# 加载环境
load_usd_environment()

# ROV 配置
rov_configs = [
    {
        "name": "ROV_Original",
        "path": "/World/ROV_Original",
        "mass": 800.0,
        "size": 1.8,
        "target_depth": -1.5,
        "color": (0.8, 0.2, 0.2),  # 红色
        "max_thrust": 800.0,
    },
    {
        "name": "ROV_Main",
        "path": "/World/ROV_Main",
        "mass": 1000.0,
        "size": 2.0,
        "target_depth": -4.0,
        "color": (0.2, 0.4, 0.8),  # 蓝色
        "max_thrust": 1200.0,
    },
    {
        "name": "ROV_Scout",
        "path": "/World/ROV_Scout",
        "mass": 500.0,
        "size": 1.6,
        "target_depth": -2.0,
        "color": (0.2, 0.8, 0.4),  # 绿色
        "max_thrust": 600.0,
    }
]

# 全局变量：ROV 物理引擎实例
rov_physics_engines = []

def create_rov_prim(stage, rov_config):
    """创建 ROV prim"""
    rov_path = rov_config["path"]
    rov_name = rov_config["name"]
    size = rov_config["size"]
    mass = rov_config["mass"]
    target_depth = rov_config["target_depth"]
    color = rov_config["color"]

    print(f"🔧 创建 {rov_name} 在 {rov_path}")

    # 创建立方体几何体
    cube_geom = UsdGeom.Cube.Define(stage, rov_path)
    cube_geom.CreateSizeAttr(size)
    cube_geom.AddTranslateOp().Set(Gf.Vec3f(0, 0, target_depth))
    cube_geom.CreateDisplayColorAttr([color])

    # 获取 prim 并添加物理属性
    prim = cube_geom.GetPrim()

    # 添加刚体 API
    rigid_body_api = UsdPhysics.RigidBodyAPI.Apply(prim)

    # 添加碰撞 API
    collision_api = UsdPhysics.CollisionAPI.Apply(prim)

    # 添加质量 API
    mass_api = UsdPhysics.MassAPI.Apply(prim)
    mass_api.CreateMassAttr(mass)

    print(f"  ✅ {rov_name} 创建完成 - 质量: {mass}kg, 尺寸: {size}m, 目标深度: {target_depth}m")

    return prim

def get_prim_position(prim):
    """获取 prim 的位置"""
    xform = prim.GetAttribute("xformOp:translate")
    if xform:
        position = xform.Get()
        if position:
            return [position[0], position[1], position[2]]
    return [0, 0, 0]

def calculate_rov_physics(rov_config, position, last_position, dt, rov_index=0):
    """
    计算 ROV 物理力（使用统一物理模块）

    参数:
        rov_config: ROV 配置字典
        position: 当前位置 [x, y, z]
        last_position: 上一帧位置 [x, y, z]
        dt: 时间步长
        rov_index: ROV 索引（用于获取对应的物理引擎）
    """
    if USE_UNIFIED_PHYSICS and rov_index < len(rov_physics_engines):
        # 使用统一物理模块计算
        try:
            # 计算速度
            velocity = [
                (position[0] - last_position[0]) / max(dt, 0.001),
                (position[1] - last_position[1]) / max(dt, 0.001),
                (position[2] - last_position[2]) / max(dt, 0.001)
            ]

            # 使用对应的物理引擎
            physics_engine = rov_physics_engines[rov_index]
            result = physics_engine.calculate_complete_physics(
                position, velocity, last_position, dt
            )

            # 返回兼容格式
            summary = result['physics_summary']
            return {
                'total_force': summary['total_force_z'],
                'buoyancy_force': summary['buoyancy_force'],
                'control_force': summary['control_force'],
                'drag_force': summary['drag_force'][2] if isinstance(summary['drag_force'], list) else summary['drag_force'],
                'depth': summary['depth'],
                'detailed_result': result  # 包含完整的计算结果
            }

        except Exception as e:
            print(f"⚠️ 统一物理计算失败，回退到简化计算: {e}")
            # 回退到简化计算
            pass

    # 简化物理计算（原始版本，作为备用）
    mass = rov_config["mass"]
    size = rov_config["size"]
    target_depth = rov_config["target_depth"]

    # 物理常数
    water_density = 1025.0
    gravity = 9.81
    volume = size ** 3
    height = size

    # 1. 计算浮力
    depth = 0.0 - position[2]  # 水面在 Z=0
    buoyancy_force = 0.0
    if depth > 0:
        submerged_ratio = min(1.0, depth / height)
        submerged_volume = volume * submerged_ratio
        buoyancy_force = water_density * submerged_volume * gravity

    # 2. 计算控制力
    depth_error = target_depth - position[2]
    control_gain = 300 if mass < 600 else 500 if mass < 900 else 700
    control_force = depth_error * control_gain

    # 3. 计算阻力
    velocity_z = (position[2] - last_position[2]) / max(dt, 0.001)
    drag_force = -velocity_z * abs(velocity_z) * 50.0

    # 4. 合力
    total_force = buoyancy_force + control_force + drag_force

    return {
        'total_force': total_force,
        'buoyancy_force': buoyancy_force,
        'control_force': control_force,
        'drag_force': drag_force,
        'depth': depth
    }

def apply_force_to_rov(prim, force_z):
    """向 ROV 应用力"""
    try:
        # 使用 PhysX 接口应用力
        prim_path = str(prim.GetPath())

        # 获取 dynamic control 接口
        dc = _dynamic_control.acquire_dynamic_control_interface()

        # 尝试应用力（这里可能需要根据具体的 Isaac Sim 版本调整）
        # 简化版本：直接修改位置（演示用）
        current_pos = get_prim_position(prim)

        # 简单的力积分（这里应该使用更精确的物理积分）
        dt = 1.0 / 60.0  # 假设 60 FPS
        acceleration = force_z / 1000.0  # 假设质量为 1000kg
        velocity_change = acceleration * dt
        new_z = current_pos[2] + velocity_change * dt

        # 更新位置
        xform = prim.GetAttribute("xformOp:translate")
        if xform:
            xform.Set(Gf.Vec3f(current_pos[0], current_pos[1], new_z))

    except Exception as e:
        pass  # 静默处理错误


# 简化版本：仅测试环境加载
def test_environment_loading_only():
    """仅测试环境加载功能，保持运行状态"""
    print("🧪 测试模式：仅环境加载")
    print("=" * 50)

    print("\n📋 初始化步骤:")
    print("1. ✅ SimulationApp 已启动")
    print("2. ✅ SimulationContext 已创建")
    print("3. ✅ PhysX 接口已获取")
    print("4. ✅ USD 环境已加载")
    print("5. 🔄 跳过物理仿真和 ROV 创建")

    # 仅启动基础仿真（不创建 ROV）
    print("\n🔄 启动基础仿真环境...")
    try:
        physx_interface.start_simulation()
        print("✅ PhysX 仿真已启动")
    except Exception as e:
        print(f"⚠️ PhysX 启动警告: {e}")

    # 启动时间轴
    try:
        sim_context._timeline.play()
        print("✅ 时间轴已启动")
    except Exception as e:
        print(f"⚠️ 时间轴启动警告: {e}")

    print("\n✅ 环境加载测试完成！")
    print("📋 检查项目:")
    print("  • Isaac Sim 窗口是否打开？")
    print("  • 是否看到了水下环境？")
    print("  • 是否加载了 ROV_THRUSTERS.usd 文件？")
    print("  • 控制台是否显示了正确的加载信息？")

    print("\n🔄 环境将保持运行状态...")
    print("💡 提示:")
    print("  • 您可以在 Isaac Sim 中自由查看环境")
    print("  • 按 Ctrl+C 可以停止程序")
    print("  • 或者直接关闭 Isaac Sim 窗口")
    print("  • 程序会持续更新以保持响应")

    # 持续运行，保持环境活跃
    print("\n⏳ 环境运行中... (按 Ctrl+C 停止)")

    try:
        frame_count = 0
        while True:
            # 保持应用更新
            simulation_app.update()

            # 每 5 秒输出一次状态
            if frame_count % 300 == 0:  # 假设 60 FPS，5秒 = 300帧
                print(f"🔄 环境运行中... 帧数: {frame_count}")

            frame_count += 1

            # 小延迟避免 CPU 占用过高
            import time
            time.sleep(1.0 / 60.0)  # 60 FPS

    except KeyboardInterrupt:
        print("\n🛑 用户中断，停止环境...")
        sim_context._timeline.stop()
        print("✅ 环境已停止")
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
        sim_context._timeline.stop()

    return True


# 完整的多ROV仿真函数（重新启用）
def run_multi_rov_simulation():
    """运行完整的多ROV物理仿真"""
    print("🚀 启动多ROV物理仿真系统...")

    print("\n📋 仿真初始化步骤:")
    print("1. ✅ SimulationApp 已启动")
    print("2. ✅ SimulationContext 已创建")
    print("3. ✅ PhysX 接口已获取")
    print("4. ✅ USD 环境已加载")
    print("5. 🔄 正在创建ROV并启动物理仿真...")

    # 启动物理仿真
    try:
        physx_interface.start_simulation()
        physx_interface.force_load_physics_from_usd()
        print("✅ PhysX 仿真已启动")
    except Exception as e:
        print(f"⚠️ PhysX 启动警告: {e}")

    # 初始化物理引擎
    global rov_physics_engines
    rov_physics_engines = []

    if USE_UNIFIED_PHYSICS:
        print("\n🔧 初始化统一物理引擎:")
        for rov_config in rov_configs:
            try:
                physics_engine = create_rov_physics_engine(
                    rov_config["mass"],
                    rov_config["size"],
                    rov_config["target_depth"],
                    rov_config.get("max_thrust", 1000.0)
                )
                rov_physics_engines.append(physics_engine)
                print(f"  ✅ {rov_config['name']}: 物理引擎已初始化")
            except Exception as e:
                print(f"  ❌ {rov_config['name']}: 物理引擎初始化失败: {e}")
                rov_physics_engines.append(None)
    else:
        print("\n⚠️ 使用简化物理计算（统一模块不可用）")

    # 创建所有 ROV
    rov_prims = []
    rov_last_positions = []

    print("\n🤖 创建ROV系统:")
    for i, rov_config in enumerate(rov_configs):
        try:
            prim = create_rov_prim(stage, rov_config)
            rov_prims.append(prim)
            rov_last_positions.append([0, 0, rov_config["target_depth"]])

            color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
            physics_status = "统一物理" if USE_UNIFIED_PHYSICS and i < len(rov_physics_engines) and rov_physics_engines[i] else "简化物理"
            print(f"  {color_emoji} {rov_config['name']}: 质量={rov_config['mass']}kg, 目标深度={rov_config['target_depth']}m ({physics_status})")
        except Exception as e:
            print(f"❌ 创建 {rov_config['name']} 失败: {e}")
            return False

    # 强制加载物理
    try:
        physx_interface.force_load_physics_from_usd()
        print("✅ 物理属性已加载")
    except Exception as e:
        print(f"⚠️ 物理加载警告: {e}")

    # 启动时间轴
    try:
        sim_context._timeline.play()
        print("✅ 仿真时间轴已启动")
    except Exception as e:
        print(f"⚠️ 时间轴启动警告: {e}")

    # 仿真参数
    simulation_time = 0.0
    dt = 1.0 / 60.0  # 60 FPS
    max_simulation_time = 60.0  # 运行 60 秒
    frame_count = 0
    last_report_time = 0.0
    report_interval = 5.0  # 每 5 秒报告一次

    print(f"\n🎯 仿真目标:")
    for rov_config in rov_configs:
        color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
        print(f"  {color_emoji} {rov_config['name']}: 目标深度 {rov_config['target_depth']}m")

    print(f"\n🔄 开始仿真循环 (最大 {max_simulation_time} 秒)...")
    print("💡 按 Ctrl+C 可以提前停止仿真")

    # 主仿真循环
    try:
        while simulation_time < max_simulation_time:
            frame_count += 1

            # 更新物理仿真
            try:
                omni.physx.acquire_physx_interface().update_simulation(
                    elapsedStep=dt,
                    currentTime=simulation_time
                )
            except Exception as e:
                if frame_count % 300 == 0:  # 每5秒报告一次错误
                    print(f"⚠️ 物理更新警告: {e}")

            # 更新每个 ROV 的物理
            for i, (rov_config, prim) in enumerate(zip(rov_configs, rov_prims)):
                if prim and prim.IsValid():
                    try:
                        # 获取当前位置
                        current_position = get_prim_position(prim)
                        last_position = rov_last_positions[i]

                        # 计算物理力（使用新的统一物理模块）
                        physics_result = calculate_rov_physics(
                            rov_config, current_position, last_position, dt, i
                        )

                        # 应用力
                        apply_force_to_rov(prim, physics_result['total_force'])

                        # 更新记录的位置
                        rov_last_positions[i] = current_position

                        # 定期输出状态
                        if frame_count % 300 == 0:  # 每 5 秒
                            depth = physics_result['depth']
                            buoyancy = physics_result['buoyancy_force']
                            control = physics_result['control_force']
                            drag = physics_result['drag_force']

                            color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"

                            # 如果使用统一物理模块，显示更详细的信息
                            if USE_UNIFIED_PHYSICS and 'detailed_result' in physics_result:
                                detailed = physics_result['detailed_result']
                                buoyancy_info = detailed['buoyancy']
                                damping_info = detailed['damping']
                                print(f"  {color_emoji} {rov_config['name']}: 深度={depth:.2f}m, "
                                      f"浮力={buoyancy:.0f}N, 控制={control:.0f}N, 阻力={drag:.0f}N")
                                print(f"    📊 浸没比例={buoyancy_info['submerged_ratio']:.2f}, "
                                      f"阻尼系数={damping_info['total_damping']:.2f}")
                            else:
                                print(f"  {color_emoji} {rov_config['name']}: 深度={depth:.2f}m, "
                                      f"浮力={buoyancy:.0f}N, 控制={control:.0f}N, 阻力={drag:.0f}N")
                    except Exception as e:
                        if frame_count % 600 == 0:  # 每10秒报告一次ROV错误
                            print(f"⚠️ ROV {rov_config['name']} 更新警告: {e}")

            # 环境效果
            if frame_count % 600 == 0:  # 每 10 秒
                wave_height = 0.4 * math.sin(simulation_time * 0.4)
                current_x = 0.3 * math.cos(simulation_time * 0.08)
                current_y = 0.3 * math.sin(simulation_time * 0.08)
                print(f"🌊 环境: 波高={wave_height:.2f}m, 洋流=[{current_x:.2f}, {current_y:.2f}]m/s")

            # 系统状态报告
            if simulation_time - last_report_time >= report_interval:
                fps = frame_count / simulation_time if simulation_time > 0 else 60
                print(f"\n=== 多ROV系统状态 (t={simulation_time:.1f}s, fps={fps:.1f}) ===")
                print("✅ 独立模式 3-ROV 水下物理系统运行中")
                print("🔴 ROV_Original: 轻型，浅水深度 (-1.5m)")
                print("🔵 ROV_Main: 重型，深海探索 (-4.0m)")
                print("🟢 ROV_Scout: 中型，侦察任务 (-2.0m)")
                last_report_time = simulation_time

            # 更新应用程序
            simulation_app.update()

            # 增加仿真时间
            simulation_time += dt

    except KeyboardInterrupt:
        print(f"\n🛑 用户中断仿真 (运行时间: {simulation_time:.1f}s)")
    except Exception as e:
        print(f"\n❌ 仿真循环错误: {e}")
        import traceback
        traceback.print_exc()

    print(f"\n🏁 仿真完成 - 总时间: {simulation_time:.1f}s, 总帧数: {frame_count}")

    # 停止仿真
    try:
        sim_context._timeline.stop()
        print("✅ 仿真时间轴已停止")
    except Exception as e:
        print(f"⚠️ 停止时间轴警告: {e}")

    return True


def main():
    """主函数 - 独立模式入口"""
    print("=" * 70)
    print("🤖 独立模式多 ROV 水下系统")
    print("� 基于官方 Isaac Sim standalone 模式最佳实践")
    print("=" * 70)

    print("\n🤖 ROV 舰队配置:")
    for rov_config in rov_configs:
        color_emoji = "🔴" if "Original" in rov_config["name"] else "🔵" if "Main" in rov_config["name"] else "🟢"
        print(f"{color_emoji} {rov_config['name']}: {rov_config['size']}m 立方体, {rov_config['mass']}kg, 目标深度 {rov_config['target_depth']}m")

    print("\n🌊 功能特性:")
    print("✅ 每个 ROV 独立物理仿真")
    print("✅ 真实的浮力、阻力和控制力")
    print("✅ 环境效果: 波浪、洋流")
    print("✅ 基于质量的自适应控制增益")
    print("✅ 多 ROV 监控和日志")
    print("✅ 独立模式，无需 ActionGraph")
    print("✅ 基于官方 Isaac Sim standalone 架构")
    print("🆕 统一物理计算模块（重构自 scripts/ 目录）")
    print("🆕 PID 控制器类，支持多种控制模式")
    print("🆕 推进器控制系统，支持 6DOF 运动")
    print("🆕 模块化设计，易于扩展和复用")

    print("\n🎯 预期行为:")
    print("• ROV_Original (红色): 轻型，快速响应，浅水深度 (-1.5m)")
    print("• ROV_Main (蓝色): 重型，稳定，深海探索 (-4.0m)")
    print("• ROV_Scout (绿色): 平衡，中等深度侦察 (-2.0m)")

    # 运行完整的多ROV仿真（重新启用）
    print("\n🚀 当前模式：完整多ROV物理仿真")
    print("🔧 环境加载 + ROV创建 + 浮力计算")

    try:
        success = run_multi_rov_simulation()
        if success:
            print("✅ 多ROV仿真完成")
        else:
            print("❌ 多ROV仿真失败")
    except Exception as e:
        print(f"❌ 仿真错误: {e}")
        import traceback
        traceback.print_exc()

    return True


if __name__ == "__main__":
    try:
        result = main()
        print(f"\n🏁 程序执行完成: {result}")
    except KeyboardInterrupt:
        print("\n🛑 用户中断仿真")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # ⚠️ 重要：关闭仿真应用程序
        print("\n🔄 关闭 Isaac Sim...")
        simulation_app.close()
