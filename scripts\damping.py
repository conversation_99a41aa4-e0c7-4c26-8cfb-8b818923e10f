"""
阻尼力计算模块
根据物体在水中的位置计算线性和角度阻尼系数
用于模拟水下物体受到的流体阻尼效应

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

# ==================== 纯 Python 接口 ====================

def calculate_damping_coefficients(z_position, height, max_damping, air_damping=0.01):
    """
    根据物体位置计算阻尼系数（纯 Python 版本）

    阻尼系数随物体浸没深度变化：
    - 完全在水面上方：最小阻尼（空气阻尼）
    - 完全在水面下方：最大阻尼（水阻尼）
    - 部分浸没：按浸没比例线性插值

    参数:
        z_position: 物体在Z轴的位置 (m)
        height: 浮体的高度 (m)
        max_damping: 最大阻尼系数（完全浸没时）
        air_damping: 空气阻尼系数，默认 0.01

    返回:
        dict: 阻尼系数结果
            {
                'linear_damping': float,      # 线性阻尼系数
                'angular_damping': float,     # 角度阻尼系数
                'damping_ratio': float,       # 阻尼比例 (0.0-1.0)
                'submerged_percentage': float # 浸没百分比 (0.0-1.0)
            }
    """
    half_height = height / 2  # 浮体半高

    # 计算浸没百分比
    # 公式：(-z_pos / height + 0.5)
    # 当z_pos = -height/2时（完全浸没），百分比 = 1.0
    # 当z_pos = height/2时（完全在水面上），百分比 = 0.0
    submerged_percentage = max(0.0, min(1.0, -z_position / height + 0.5))

    # 根据位置计算阻尼系数
    if z_position >= half_height:
        # 物体完全在水面上方：使用最小阻尼（空气阻尼）
        damping = air_damping
        damping_ratio = 0.0
    elif z_position <= -half_height:
        # 物体完全在水面下方：使用最大阻尼（水阻尼）
        damping = max_damping
        damping_ratio = 1.0
    else:
        # 物体部分浸没：按浸没比例计算阻尼
        damping_ratio = submerged_percentage
        damping = air_damping + (max_damping - air_damping) * damping_ratio

    return {
        'linear_damping': damping,
        'angular_damping': damping,  # 通常线性和角度阻尼相同
        'damping_ratio': damping_ratio,
        'submerged_percentage': submerged_percentage
    }

def calculate_drag_force(velocity, damping_coefficient, drag_model='quadratic'):
    """
    根据速度和阻尼系数计算阻力（纯 Python 版本）

    参数:
        velocity: 速度向量 [vx, vy, vz] (m/s)
        damping_coefficient: 阻尼系数
        drag_model: 阻力模型，'linear' 或 'quadratic'

    返回:
        list: 阻力向量 [fx, fy, fz] (N)
    """
    if isinstance(velocity, (int, float)):
        # 单轴速度
        if drag_model == 'quadratic':
            drag = -velocity * abs(velocity) * damping_coefficient
        else:  # linear
            drag = -velocity * damping_coefficient
        return drag
    else:
        # 速度向量
        drag_force = []
        for v in velocity:
            if drag_model == 'quadratic':
                drag = -v * abs(v) * damping_coefficient
            else:  # linear
                drag = -v * damping_coefficient
            drag_force.append(drag)
        return drag_force

def calculate_environmental_damping(z_position, height, velocity, max_damping=50.0, air_damping=0.01):
    """
    计算环境阻尼力（综合函数）

    参数:
        z_position: 物体Z轴位置 (m)
        height: 物体高度 (m)
        velocity: 速度向量 [vx, vy, vz] (m/s)
        max_damping: 最大阻尼系数
        air_damping: 空气阻尼系数

    返回:
        dict: 完整的阻尼计算结果
            {
                'damping_coefficients': dict,  # 阻尼系数信息
                'drag_force': list,           # 阻力向量
                'total_damping': float        # 总阻尼系数
            }
    """
    # 计算阻尼系数
    damping_info = calculate_damping_coefficients(z_position, height, max_damping, air_damping)

    # 计算阻力
    drag_force = calculate_drag_force(velocity, damping_info['linear_damping'], 'quadratic')

    return {
        'damping_coefficients': damping_info,
        'drag_force': drag_force,
        'total_damping': damping_info['linear_damping']
    }

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    根据物体位置计算阻尼系数（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                z_position: 物体在Z轴的位置 (m)
                max_damping: 最大阻尼系数（完全浸没时）
                floating_obj_height: 浮体的高度 (m)
            输出参数:
                linear_damping: 线性阻尼系数
                angular_damping: 角度阻尼系数
    """
    # 获取输入参数
    z_pos = db.inputs.z_position
    max_damp = db.inputs.max_damping
    height = db.inputs.floating_obj_height

    # 使用纯 Python 函数计算
    result = calculate_damping_coefficients(z_pos, height, max_damp)

    # 输出结果
    db.outputs.linear_damping = result['linear_damping']
    db.outputs.angular_damping = result['angular_damping']
    