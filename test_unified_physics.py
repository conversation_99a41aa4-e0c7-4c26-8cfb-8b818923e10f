"""
测试统一物理计算模块
演示如何在纯 Python 环境中使用重构后的物理计算功能
"""

import sys
import os

# 添加 scripts 目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
scripts_dir = os.path.join(current_dir, "scripts")
if scripts_dir not in sys.path:
    sys.path.append(scripts_dir)

# 导入重构后的模块
try:
    from scripts.rov_physics_unified import (
        ROVPhysicsEngine, create_rov_physics_engine, calculate_simple_rov_physics,
        calculate_rov_rotated_buoyancy, calculate_rov_buoyancy_torque
    )
    from scripts.buoyancy_control import calculate_buoyancy_force
    from scripts.buoyancy_forces import calculate_rotated_buoyancy_force, calculate_buoyancy_torque
    from scripts.controller import PIDController
    from scripts.damping import calculate_damping_coefficients, calculate_drag_force
    from scripts.linear_angular_control import ThrusterController, calculate_thruster_allocation
    print("✅ 成功导入所有重构后的物理计算模块（包括旋转浮力）")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_individual_modules():
    """测试各个独立模块"""
    print("\n" + "="*50)
    print("🧪 测试各个独立模块")
    print("="*50)
    
    # 1. 测试浮力计算
    print("\n1️⃣ 测试浮力计算模块:")
    volume = 8.0  # 2m x 2m x 2m 立方体
    height = 2.0
    z_position = -1.0  # 1米深度
    
    buoyancy_result = calculate_buoyancy_force(volume, height, z_position)
    print(f"   体积: {volume}m³, 高度: {height}m, 深度: {-z_position}m")
    print(f"   浮力: {buoyancy_result['buoyancy_force']:.1f}N")
    print(f"   浸没比例: {buoyancy_result['submerged_ratio']:.2f}")
    
    # 2. 测试 PID 控制器
    print("\n2️⃣ 测试 PID 控制器:")
    controller = PIDController(kp=100, ki=10, kd=0.01)
    
    target_depth = -2.0
    current_depth = -1.5
    
    for i in range(3):
        control_output = controller.compute(target_depth, current_depth)
        print(f"   步骤 {i+1}: 目标={target_depth}m, 当前={current_depth}m, 控制输出={control_output:.1f}N")
        current_depth += control_output * 0.001  # 模拟响应
    
    # 3. 测试阻尼计算
    print("\n3️⃣ 测试阻尼计算:")
    damping_result = calculate_damping_coefficients(z_position, height, max_damping=50.0)
    print(f"   位置: {z_position}m, 高度: {height}m")
    print(f"   线性阻尼: {damping_result['linear_damping']:.2f}")
    print(f"   浸没百分比: {damping_result['submerged_percentage']:.2f}")
    
    velocity = [0, 0, -0.5]  # 下沉速度
    drag_force = calculate_drag_force(velocity, damping_result['linear_damping'])
    print(f"   速度: {velocity} m/s")
    print(f"   阻力: {drag_force} N")
    
    # 4. 测试推进器控制
    print("\n4️⃣ 测试推进器控制:")
    thruster_controller = ThrusterController(max_thrust=1000.0)

    # 模拟操纵杆输入
    joystick_inputs = [
        {'y': 0.5, 'x': 0.0, 'desc': '前进'},
        {'y': 0.0, 'x': 0.3, 'desc': '右转'},
        {'y': 0.3, 'x': 0.2, 'desc': '前进+右转'}
    ]

    for input_data in joystick_inputs:
        thruster_result = calculate_thruster_allocation(input_data['y'], input_data['x'])
        print(f"   {input_data['desc']} (y={input_data['y']}, x={input_data['x']}):")
        print(f"     左前: {thruster_result['left_front'][2]:.1f}N")
        print(f"     右前: {thruster_result['right_front'][2]:.1f}N")

    # 5. 测试旋转浮力计算
    print("\n5️⃣ 测试旋转浮力计算:")
    volume = 8.0  # 2m x 2m x 2m 立方体
    height = 2.0
    z_position = -1.0  # 1米深度

    # 测试不同旋转角度
    rotation_tests = [
        {'angles': [0, 0, 0], 'desc': '无旋转'},
        {'angles': [30, 0, 0], 'desc': '横滚30度'},
        {'angles': [0, 45, 0], 'desc': '俯仰45度'},
        {'angles': [15, 30, 45], 'desc': '复合旋转'}
    ]

    for test in rotation_tests:
        result = calculate_rotated_buoyancy_force(volume, height, z_position, test['angles'])
        force = result['force_vector']
        print(f"   {test['desc']}: 浮力=[{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}]N, "
              f"浸没比例={result['submerged_ratio']:.2f}")

    # 6. 测试浮力力矩
    print("\n6️⃣ 测试浮力力矩:")
    torque_result = calculate_buoyancy_torque(
        volume, height, z_position, [10, 20, 30], [0.1, 0.0, 0.2]
    )
    force = torque_result['force_vector']
    torque = torque_result['torque_vector']
    print(f"   浮力: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
    print(f"   力矩: [{torque[0]:.1f}, {torque[1]:.1f}, {torque[2]:.1f}] N·m")

def test_unified_engine():
    """测试统一物理引擎"""
    print("\n" + "="*50)
    print("🚀 测试统一物理引擎")
    print("="*50)
    
    # ROV 配置（与 standalone_multi_rov_system.py 中相同）
    rov_configs = [
        {'mass': 800, 'size': 1.8, 'target_depth': -1.5, 'name': 'ROV_Original'},
        {'mass': 1000, 'size': 2.0, 'target_depth': -4.0, 'name': 'ROV_Main'},
        {'mass': 500, 'size': 1.6, 'target_depth': -2.0, 'name': 'ROV_Scout'}
    ]
    
    # 为每个 ROV 创建物理引擎
    physics_engines = []
    for config in rov_configs:
        engine = create_rov_physics_engine(
            config['mass'], config['size'], config['target_depth']
        )
        physics_engines.append(engine)
        print(f"✅ {config['name']} 物理引擎已创建")
    
    # 模拟仿真步骤
    print(f"\n🔄 模拟 5 个仿真步骤:")
    dt = 1.0 / 60.0  # 60 FPS
    
    for step in range(5):
        print(f"\n--- 步骤 {step + 1} ---")
        
        for i, (config, engine) in enumerate(zip(rov_configs, physics_engines)):
            # 模拟位置（从水面开始下沉）
            current_z = -0.5 - step * 0.3  # 逐渐下沉
            position = [0, 0, current_z]
            velocity = [0, 0, -0.3]  # 下沉速度
            last_position = [0, 0, current_z + 0.3]
            
            # 计算完整物理状态
            result = engine.calculate_complete_physics(
                position, velocity, last_position, dt
            )
            
            summary = result['physics_summary']
            color_emoji = "🔴" if "Original" in config["name"] else "🔵" if "Main" in config["name"] else "🟢"
            
            print(f"{color_emoji} {config['name']}: 深度={summary['depth']:.2f}m, "
                  f"浮力={summary['buoyancy_force']:.0f}N, "
                  f"控制={summary['control_force']:.0f}N, "
                  f"总力={summary['total_force_z']:.0f}N")

def test_simple_physics_function():
    """测试简化的物理计算函数"""
    print("\n" + "="*50)
    print("⚡ 测试简化物理计算函数")
    print("="*50)
    
    # 测试参数
    mass = 1000.0
    size = 2.0
    target_depth = -3.0
    current_position = [0, 0, -1.5]
    velocity = [0, 0, -0.2]
    dt = 1.0 / 60.0
    
    # 使用简化函数计算
    result = calculate_simple_rov_physics(
        mass, size, target_depth, current_position, velocity, dt
    )
    
    print(f"ROV 参数: 质量={mass}kg, 尺寸={size}m, 目标深度={target_depth}m")
    print(f"当前状态: 位置={current_position}, 速度={velocity}")
    print(f"计算结果:")
    print(f"  深度: {result['depth']:.2f}m")
    print(f"  浮力: {result['buoyancy_force']:.1f}N")
    print(f"  控制力: {result['control_force']:.1f}N")
    print(f"  阻力: {result['drag_force']:.1f}N")
    print(f"  总力: {result['total_force_z']:.1f}N")

def test_rotated_buoyancy_module():
    """专门测试旋转浮力模块"""
    print("\n" + "="*50)
    print("🔄 测试旋转浮力模块（buoyancy_forces.py）")
    print("="*50)

    # ROV 参数
    mass = 1000.0
    size = 2.0
    position = [0, 0, -1.5]  # 1.5米深度

    print(f"ROV 参数: 质量={mass}kg, 尺寸={size}m, 位置={position}")

    # 1. 测试便捷函数
    print(f"\n1️⃣ 测试便捷函数:")
    rotation_angles = [15, 30, 45]  # 复合旋转

    buoyancy_result = calculate_rov_rotated_buoyancy(
        mass, size, position, rotation_angles
    )

    force = buoyancy_result['force_vector']
    print(f"   旋转角度: {rotation_angles} 度")
    print(f"   旋转浮力: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
    print(f"   原始浮力: {buoyancy_result['original_force']:.1f} N")
    print(f"   浸没比例: {buoyancy_result['submerged_ratio']:.2f}")

    # 2. 测试力矩计算
    print(f"\n2️⃣ 测试力矩计算:")
    center_offset = [0.1, 0.0, 0.2]  # 浮心偏移

    torque_result = calculate_rov_buoyancy_torque(
        mass, size, position, rotation_angles, center_offset
    )

    torque = torque_result['torque_vector']
    cob = torque_result['center_of_buoyancy']
    print(f"   浮心偏移: {center_offset} m")
    print(f"   力矩: [{torque[0]:.1f}, {torque[1]:.1f}, {torque[2]:.1f}] N·m")
    print(f"   浮心位置: [{cob[0]:.2f}, {cob[1]:.2f}, {cob[2]:.2f}] m")

    # 3. 对比不同旋转角度的影响
    print(f"\n3️⃣ 旋转角度影响对比:")
    test_rotations = [
        [0, 0, 0],      # 无旋转
        [90, 0, 0],     # 横滚90度
        [0, 90, 0],     # 俯仰90度
        [0, 0, 90]      # 偏航90度
    ]

    for rotation in test_rotations:
        result = calculate_rov_rotated_buoyancy(mass, size, position, rotation)
        force = result['force_vector']
        total_force = (force[0]**2 + force[1]**2 + force[2]**2)**0.5
        print(f"   旋转{rotation}: 总力={total_force:.1f}N, Z分量={force[2]:.1f}N")

def compare_with_original():
    """与原始计算方法对比"""
    print("\n" + "="*50)
    print("📊 与原始计算方法对比")
    print("="*50)
    
    # 测试参数
    mass = 800.0
    size = 1.8
    target_depth = -1.5
    position = [0, 0, -1.0]
    last_position = [0, 0, -0.9]
    dt = 1.0 / 60.0
    
    # 1. 使用新的统一物理模块
    print("🆕 新的统一物理模块:")
    velocity = [(position[i] - last_position[i]) / dt for i in range(3)]
    new_result = calculate_simple_rov_physics(mass, size, target_depth, position, velocity, dt)
    
    print(f"  浮力: {new_result['buoyancy_force']:.1f}N")
    print(f"  控制力: {new_result['control_force']:.1f}N")
    print(f"  阻力: {new_result['drag_force']:.1f}N")
    print(f"  总力: {new_result['total_force_z']:.1f}N")
    
    # 2. 原始简化计算（模拟原来的方法）
    print("\n📜 原始简化计算:")
    
    # 物理常数
    water_density = 1025.0
    gravity = 9.81
    volume = size ** 3
    height = size
    
    # 浮力
    depth = 0.0 - position[2]
    buoyancy_force = 0.0
    if depth > 0:
        submerged_ratio = min(1.0, depth / height)
        submerged_volume = volume * submerged_ratio
        buoyancy_force = water_density * submerged_volume * gravity
    
    # 控制力
    depth_error = target_depth - position[2]
    control_gain = 300 if mass < 600 else 500 if mass < 900 else 700
    control_force = depth_error * control_gain
    
    # 阻力
    velocity_z = (position[2] - last_position[2]) / max(dt, 0.001)
    drag_force = -velocity_z * abs(velocity_z) * 50.0
    
    # 总力
    total_force = buoyancy_force + control_force + drag_force
    
    print(f"  浮力: {buoyancy_force:.1f}N")
    print(f"  控制力: {control_force:.1f}N")
    print(f"  阻力: {drag_force:.1f}N")
    print(f"  总力: {total_force:.1f}N")
    
    # 3. 对比结果
    print(f"\n📈 对比结果:")
    print(f"  浮力差异: {abs(new_result['buoyancy_force'] - buoyancy_force):.1f}N")
    print(f"  控制力差异: {abs(new_result['control_force'] - control_force):.1f}N")
    print(f"  总力差异: {abs(new_result['total_force_z'] - total_force):.1f}N")

def main():
    """主测试函数"""
    print("🧪 ROV 统一物理计算模块测试")
    print("📋 测试重构后的 scripts/ 目录中的物理计算功能")
    print("🎯 验证从 ActionGraph 模式转换为纯 Python 调用的正确性")
    
    try:
        # 运行各项测试
        test_individual_modules()
        test_unified_engine()
        test_simple_physics_function()
        test_rotated_buoyancy_module()
        compare_with_original()
        
        print("\n" + "="*50)
        print("✅ 所有测试完成！")
        print("💡 现在您可以在其他 Python 脚本中直接调用这些物理计算功能")
        print("🔗 使用方法:")
        print("   from scripts.rov_physics_unified import create_rov_physics_engine")
        print("   engine = create_rov_physics_engine(mass, size, target_depth)")
        print("   result = engine.calculate_complete_physics(position, velocity, last_pos, dt)")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
