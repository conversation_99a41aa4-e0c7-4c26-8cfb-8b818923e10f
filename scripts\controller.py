"""
PID控制器模块
实现比例-积分-微分(PID)控制算法，用于水下机器人的姿态控制

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

# ==================== 纯 Python 接口 ====================

class PIDController:
    """
    PID控制器类（纯 Python 版本）

    实现标准的PID控制算法，支持多种控制模式
    """

    def __init__(self, kp=100.0, ki=10.0, kd=0.01, sat_max=1000.0, sat_min=-1000.0, dt=0.01667):
        """
        初始化PID控制器

        参数:
            kp: 比例增益
            ki: 积分增益
            kd: 微分增益
            sat_max: 输出上限
            sat_min: 输出下限
            dt: 时间步长
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.sat_max = sat_max
        self.sat_min = sat_min
        self.dt = dt

        # 内部状态
        self.error_integral = 0.0
        self.error_prev = 0.0
        self.initialized = False

    def reset(self):
        """重置控制器状态"""
        self.error_integral = 0.0
        self.error_prev = 0.0
        self.initialized = False

    def compute(self, setpoint, measurement):
        """
        计算PID控制输出

        参数:
            setpoint: 目标值
            measurement: 当前测量值

        返回:
            float: 控制输出
        """
        # 计算误差
        error = setpoint - measurement

        # 首次运行时初始化
        if not self.initialized:
            self.error_prev = error
            self.initialized = True

        # 更新积分项
        self.error_integral += error

        # 计算各项
        proportional = self.kp * error
        integral = self.ki * self.error_integral * self.dt
        derivative = self.kd * (error - self.error_prev) / self.dt

        # 计算总输出
        output = proportional + integral + derivative

        # 饱和限制
        if output > self.sat_max:
            output = self.sat_max
        elif output < self.sat_min:
            output = self.sat_min

        # 更新前一次误差
        self.error_prev = error

        return output

def calculate_depth_control_force(current_depth, target_depth, controller=None, dive_force=0.0):
    """
    计算深度控制力（纯 Python 版本）

    参数:
        current_depth: 当前深度 (m)
        target_depth: 目标深度 (m)
        controller: PID控制器实例，如果为None则创建默认控制器
        dive_force: 额外的潜水力 (N)

    返回:
        dict: 控制力结果
            {
                'control_force': float,      # 控制力输出
                'total_force': float,        # 总力（控制力+潜水力）
                'error': float,              # 深度误差
                'force_vector': [x,y,z],     # 力向量
                'minus_force_vector': [x,y,z] # 反向力向量
            }
    """
    if controller is None:
        controller = PIDController()

    # 计算控制输出
    control_output = controller.compute(target_depth, current_depth)

    # 计算总力
    total_force = control_output + dive_force
    minus_total_force = -control_output + dive_force

    return {
        'control_force': control_output,
        'total_force': total_force,
        'error': target_depth - current_depth,
        'force_vector': [0, 0, total_force],
        'minus_force_vector': [0, 0, minus_total_force]
    }

def calculate_attitude_control_force(current_orientation, target_orientation=0.0, controller=None, dive_force=0.0):
    """
    计算姿态控制力（纯 Python 版本）

    参数:
        current_orientation: 当前姿态角度 (度)
        target_orientation: 目标姿态角度 (度)，默认0
        controller: PID控制器实例
        dive_force: 额外的潜水力 (N)

    返回:
        dict: 控制力结果
    """
    if controller is None:
        controller = PIDController()

    # 计算控制输出
    control_output = controller.compute(target_orientation, current_orientation)

    # 计算总力
    total_force = control_output + dive_force
    minus_total_force = -control_output + dive_force

    return {
        'control_force': control_output,
        'total_force': total_force,
        'error': target_orientation - current_orientation,
        'force_vector': [0, 0, total_force],
        'minus_force_vector': [0, 0, minus_total_force]
    }

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化PID控制器参数和变量（ActionGraph 兼容）
    """
    # 控制输出饱和限制设置
    db.sat_max = 1000   # 最大控制输出限制
    db.sat_min = -1000  # 最小控制输出限制

    # PID控制器增益参数设置
    db.kp = 100   # 比例增益
    db.ki = 10    # 积分增益
    db.kd = 0.01  # 微分增益

    # PID控制器内部状态变量初始化
    db.error_integral = 0  # 误差积分累积值
    db.error_prev = 0      # 前一次误差值

    # 控制周期时间步长
    db.time = 0.01667  # 时间步长 ≈ 1/60 秒

def compute(db):
    """
    计算PID控制输出（ActionGraph 兼容版本）
    """
    # 获取输入参数
    orientation = db.inputs.orientation
    dive_force = db.inputs.dive_force

    # 使用纯 Python 函数计算（创建临时控制器）
    temp_controller = PIDController(
        kp=db.kp, ki=db.ki, kd=db.kd,
        sat_max=db.sat_max, sat_min=db.sat_min,
        dt=db.time
    )
    temp_controller.error_integral = db.error_integral
    temp_controller.error_prev = db.error_prev
    temp_controller.initialized = True

    result = calculate_attitude_control_force(orientation, 0.0, temp_controller, dive_force)

    # 更新状态
    db.error_integral = temp_controller.error_integral
    db.error_prev = temp_controller.error_prev

    # 输出结果
    db.outputs.force = result['force_vector']
    db.outputs.minus_force = result['minus_force_vector']
